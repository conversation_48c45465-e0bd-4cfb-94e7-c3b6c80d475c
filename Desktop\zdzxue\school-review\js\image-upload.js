/**
 * 123云盘图床上传功能
 * 基于123云盘开放平台API实现图片上传
 */

class ImageUploader {
    constructor() {
        // 123云盘API配置
        this.apiConfig = {
            baseUrl: 'https://open-api.123pan.com',
            uploadUrl: 'https://open-api.123pan.com/upload/v1/oss',
            platform: 'open_platform',
            parentFileID: window.CLOUD123_PARENT_FILE_ID || '' // 图床目录ID
        };

        // 认证相关
        this.auth = {
            accessToken: null,
            expiredAt: null,
            isRefreshing: false
        };

        // 上传状态
        this.uploadQueue = [];
        this.isUploading = false;

        // 支持的图片格式
        this.supportedFormats = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];

        // 最大文件大小 (10MB)
        this.maxFileSize = 10 * 1024 * 1024;

        // 初始化
        this.init();
    }

    /**
     * 初始化上传器
     */
    init() {
        console.log('图片上传器初始化完成');

        // 预加载access_token
        this.getAccessToken().catch(error => {
            console.warn('预加载access_token失败:', error.message);
        });
    }

    /**
     * 获取有效的access_token
     * @returns {Promise<string>} access_token
     */
    async getAccessToken() {
        try {
            // 检查缓存的令牌是否有效
            if (this.isTokenValid()) {
                return this.auth.accessToken;
            }

            // 如果正在刷新令牌，等待完成
            if (this.auth.isRefreshing) {
                return await this.waitForTokenRefresh();
            }

            // 获取新的令牌
            return await this.refreshAccessToken();

        } catch (error) {
            console.error('获取access_token失败:', error);
            throw new Error('无法获取123云盘访问令牌');
        }
    }

    /**
     * 检查令牌是否有效
     * @returns {boolean} 是否有效
     */
    isTokenValid() {
        if (!this.auth.accessToken || !this.auth.expiredAt) {
            return false;
        }

        // 提前5分钟刷新令牌
        const now = new Date();
        const expiredAt = new Date(this.auth.expiredAt);
        const refreshTime = new Date(expiredAt.getTime() - 5 * 60 * 1000);

        return now < refreshTime;
    }

    /**
     * 刷新access_token
     * @returns {Promise<string>} 新的access_token
     */
    async refreshAccessToken() {
        this.auth.isRefreshing = true;

        try {
            const response = await fetch('/api/cloud123-token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();

            if (result.success && result.data) {
                // 更新缓存
                this.auth.accessToken = result.data.accessToken;
                this.auth.expiredAt = result.data.expiredAt;
                this.auth.isRefreshing = false;

                console.log('123云盘access_token获取成功');
                return result.data.accessToken;

            } else {
                throw new Error(result.error || '获取access_token失败');
            }

        } catch (error) {
            this.auth.isRefreshing = false;
            console.error('刷新access_token失败:', error);
            throw error;
        }
    }

    /**
     * 等待令牌刷新完成
     * @returns {Promise<string>} access_token
     */
    async waitForTokenRefresh() {
        const maxWaitTime = 10000; // 最大等待10秒
        const checkInterval = 100; // 每100ms检查一次
        let waitTime = 0;

        return new Promise((resolve, reject) => {
            const checkToken = () => {
                if (!this.auth.isRefreshing && this.auth.accessToken) {
                    resolve(this.auth.accessToken);
                } else if (waitTime >= maxWaitTime) {
                    reject(new Error('等待令牌刷新超时'));
                } else {
                    waitTime += checkInterval;
                    setTimeout(checkToken, checkInterval);
                }
            };

            checkToken();
        });
    }

    /**
     * 验证文件
     * @param {File} file - 要验证的文件
     * @returns {Object} 验证结果
     */
    validateFile(file) {
        const result = {
            valid: true,
            error: null
        };

        // 检查文件类型
        const fileExtension = file.name.split('.').pop().toLowerCase();
        if (!this.supportedFormats.includes(fileExtension)) {
            result.valid = false;
            result.error = `不支持的文件格式。支持的格式：${this.supportedFormats.join(', ')}`;
            return result;
        }

        // 检查文件大小
        if (file.size > this.maxFileSize) {
            result.valid = false;
            result.error = `文件大小超过限制。最大允许：${this.formatFileSize(this.maxFileSize)}`;
            return result;
        }

        // 检查文件是否为图片
        if (!file.type.startsWith('image/')) {
            result.valid = false;
            result.error = '请选择有效的图片文件';
            return result;
        }

        return result;
    }

    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 计算文件MD5
     * @param {File} file - 文件对象
     * @returns {Promise<string>} MD5值
     */
    async calculateMD5(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = function(e) {
                // 这里使用简化的MD5计算，实际项目中应该使用专业的MD5库
                // 为了演示，我们使用文件名和大小生成一个伪MD5
                const content = e.target.result;
                const hash = btoa(file.name + file.size + file.lastModified).replace(/[^a-zA-Z0-9]/g, '').toLowerCase().substring(0, 32);
                resolve(hash);
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(file);
        });
    }

    /**
     * 创建文件（第一步）
     * @param {File} file - 文件对象
     * @param {string} etag - 文件MD5
     * @returns {Promise<Object>} 创建结果
     */
    async createFile(file, etag) {
        const url = `${this.apiConfig.uploadUrl}/file/create`;
        const accessToken = await this.getAccessToken();

        const requestData = {
            parentFileID: this.apiConfig.parentFileID,
            filename: file.name,
            etag: etag,
            size: file.size,
            type: 1 // 图床类型
        };

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Platform': this.apiConfig.platform,
                    'Authorization': `Bearer ${accessToken}`
                },
                body: JSON.stringify(requestData)
            });

            const result = await response.json();

            if (result.code === 0) {
                return {
                    success: true,
                    data: result.data
                };
            } else {
                throw new Error(result.message || '创建文件失败');
            }
        } catch (error) {
            console.error('创建文件失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 获取上传地址（第二步）
     * @param {string} preuploadID - 预上传ID
     * @param {number} sliceNo - 分片序号
     * @returns {Promise<Object>} 上传地址
     */
    async getUploadUrl(preuploadID, sliceNo = 1) {
        const url = `${this.apiConfig.uploadUrl}/file/get_upload_url`;

        const requestData = {
            preuploadID: preuploadID,
            sliceNo: sliceNo
        };

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Platform': this.apiConfig.platform,
                    'Authorization': `Bearer ${this.apiConfig.accessToken}`
                },
                body: JSON.stringify(requestData)
            });

            const result = await response.json();

            if (result.code === 0) {
                return {
                    success: true,
                    data: result.data
                };
            } else {
                throw new Error(result.message || '获取上传地址失败');
            }
        } catch (error) {
            console.error('获取上传地址失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 上传文件分片（第三步）
     * @param {string} presignedURL - 预签名上传地址
     * @param {File} file - 文件对象
     * @param {Function} onProgress - 进度回调
     * @returns {Promise<Object>} 上传结果
     */
    async uploadFileSlice(presignedURL, file, onProgress) {
        try {
            const xhr = new XMLHttpRequest();

            return new Promise((resolve, reject) => {
                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable && onProgress) {
                        const progress = Math.round((e.loaded / e.total) * 100);
                        onProgress(progress);
                    }
                });

                xhr.addEventListener('load', () => {
                    if (xhr.status === 200) {
                        resolve({
                            success: true,
                            data: xhr.response
                        });
                    } else {
                        reject(new Error(`上传失败: ${xhr.status} ${xhr.statusText}`));
                    }
                });

                xhr.addEventListener('error', () => {
                    reject(new Error('网络错误，上传失败'));
                });

                xhr.open('PUT', presignedURL);
                xhr.setRequestHeader('Content-Type', 'application/octet-stream');
                xhr.send(file);
            });
        } catch (error) {
            console.error('上传文件分片失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 完成上传（第四步）
     * @param {string} preuploadID - 预上传ID
     * @returns {Promise<Object>} 完成结果
     */
    async completeUpload(preuploadID) {
        const url = `${this.apiConfig.uploadUrl}/file/upload_complete`;

        const requestData = {
            preuploadID: preuploadID
        };

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Platform': this.apiConfig.platform,
                    'Authorization': `Bearer ${this.apiConfig.accessToken}`
                },
                body: JSON.stringify(requestData)
            });

            const result = await response.json();

            if (result.code === 0) {
                return {
                    success: true,
                    data: result.data
                };
            } else {
                throw new Error(result.message || '完成上传失败');
            }
        } catch (error) {
            console.error('完成上传失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 轮询获取上传结果（第五步，如果需要）
     * @param {string} preuploadID - 预上传ID
     * @returns {Promise<Object>} 最终结果
     */
    async pollUploadResult(preuploadID) {
        const url = `${this.apiConfig.uploadUrl}/file/upload_async_result`;

        const requestData = {
            preuploadID: preuploadID
        };

        const maxAttempts = 30; // 最多轮询30次
        let attempts = 0;

        while (attempts < maxAttempts) {
            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Platform': this.apiConfig.platform,
                        'Authorization': `Bearer ${this.apiConfig.accessToken}`
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();

                if (result.code === 0) {
                    if (result.data.completed) {
                        return {
                            success: true,
                            data: result.data
                        };
                    }
                } else {
                    throw new Error(result.message || '获取上传结果失败');
                }

                // 等待1秒后重试
                await new Promise(resolve => setTimeout(resolve, 1000));
                attempts++;
            } catch (error) {
                console.error('轮询上传结果失败:', error);
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        return {
            success: false,
            error: '上传超时，请稍后重试'
        };
    }

    /**
     * 获取图片访问URL
     * @param {string} fileID - 文件ID
     * @returns {Promise<string>} 图片URL
     */
    async getImageUrl(fileID) {
        // 这里应该调用获取直链的API
        // 为了演示，我们返回一个模拟的URL
        return `https://img.123pan.cn/${fileID}`;
    }

    /**
     * 上传单个文件（完整流程）
     * @param {File} file - 文件对象
     * @param {Function} onProgress - 进度回调
     * @returns {Promise<Object>} 上传结果
     */
    async uploadFile(file, onProgress) {
        try {
            // 1. 验证文件
            const validation = this.validateFile(file);
            if (!validation.valid) {
                throw new Error(validation.error);
            }

            // 检查API配置
            if (!this.apiConfig.accessToken) {
                // 如果没有配置API，使用本地预览
                return this.createLocalPreview(file);
            }

            // 2. 计算文件MD5
            onProgress && onProgress(10, '计算文件校验值...');
            const etag = await this.calculateMD5(file);

            // 3. 创建文件
            onProgress && onProgress(20, '创建文件记录...');
            const createResult = await this.createFile(file, etag);
            if (!createResult.success) {
                throw new Error(createResult.error);
            }

            // 检查是否秒传
            if (createResult.data.reuse) {
                onProgress && onProgress(100, '文件已存在，秒传成功');
                const imageUrl = await this.getImageUrl(createResult.data.fileID);
                return {
                    success: true,
                    url: imageUrl,
                    fileID: createResult.data.fileID,
                    filename: file.name
                };
            }

            // 4. 获取上传地址
            onProgress && onProgress(30, '获取上传地址...');
            const uploadUrlResult = await this.getUploadUrl(createResult.data.preuploadID);
            if (!uploadUrlResult.success) {
                throw new Error(uploadUrlResult.error);
            }

            // 5. 上传文件
            onProgress && onProgress(40, '上传文件中...');
            const uploadResult = await this.uploadFileSlice(
                uploadUrlResult.data.presignedURL,
                file,
                (progress) => {
                    const totalProgress = 40 + (progress * 0.4); // 40-80%
                    onProgress && onProgress(totalProgress, '上传文件中...');
                }
            );

            if (!uploadResult.success) {
                throw new Error(uploadResult.error);
            }

            // 6. 完成上传
            onProgress && onProgress(85, '完成上传...');
            const completeResult = await this.completeUpload(createResult.data.preuploadID);
            if (!completeResult.success) {
                throw new Error(completeResult.error);
            }

            // 7. 如果需要异步处理，轮询结果
            let finalResult = completeResult.data;
            if (completeResult.data.async) {
                onProgress && onProgress(90, '处理文件中...');
                const pollResult = await this.pollUploadResult(createResult.data.preuploadID);
                if (!pollResult.success) {
                    throw new Error(pollResult.error);
                }
                finalResult = pollResult.data;
            }

            // 8. 获取图片访问URL
            onProgress && onProgress(95, '生成访问链接...');
            const imageUrl = await this.getImageUrl(finalResult.fileID);

            onProgress && onProgress(100, '上传完成');

            return {
                success: true,
                url: imageUrl,
                fileID: finalResult.fileID,
                filename: file.name
            };

        } catch (error) {
            console.error('上传文件失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 创建本地预览（当API未配置时使用）
     * @param {File} file - 文件对象
     * @returns {Promise<Object>} 本地预览结果
     */
    async createLocalPreview(file) {
        return new Promise((resolve) => {
            const reader = new FileReader();
            reader.onload = function(e) {
                resolve({
                    success: true,
                    url: e.target.result,
                    fileID: 'local_' + Date.now(),
                    filename: file.name,
                    isLocal: true
                });
            };
            reader.readAsDataURL(file);
        });
    }

    /**
     * 批量上传文件
     * @param {FileList|Array} files - 文件列表
     * @param {Function} onProgress - 进度回调
     * @param {Function} onFileComplete - 单个文件完成回调
     * @returns {Promise<Array>} 上传结果列表
     */
    async uploadFiles(files, onProgress, onFileComplete) {
        const results = [];
        const totalFiles = files.length;

        for (let i = 0; i < totalFiles; i++) {
            const file = files[i];

            try {
                const result = await this.uploadFile(file, (progress, status) => {
                    const totalProgress = ((i / totalFiles) + (progress / 100 / totalFiles)) * 100;
                    onProgress && onProgress(totalProgress, `${status} (${i + 1}/${totalFiles})`);
                });

                results.push(result);
                onFileComplete && onFileComplete(result, i + 1, totalFiles);

            } catch (error) {
                const errorResult = {
                    success: false,
                    error: error.message,
                    filename: file.name
                };
                results.push(errorResult);
                onFileComplete && onFileComplete(errorResult, i + 1, totalFiles);
            }
        }

        return results;
    }

    /**
     * 取消上传
     * @param {string} uploadId - 上传ID
     */
    cancelUpload(uploadId) {
        // 实现取消上传逻辑
        console.log('取消上传:', uploadId);
    }

    /**
     * 清理临时文件
     */
    cleanup() {
        this.uploadQueue = [];
        console.log('清理上传队列完成');
    }
}

// 导出上传器类
window.ImageUploader = ImageUploader;
