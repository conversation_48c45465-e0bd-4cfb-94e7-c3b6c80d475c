/**
 * 123云盘认证API服务
 * 用于获取和管理access_token
 */

class Cloud123AuthService {
    constructor() {
        // API配置
        this.apiConfig = {
            baseUrl: 'https://open-api.123pan.com',
            platform: 'open_platform'
        };

        // 令牌缓存
        this.tokenCache = {
            accessToken: null,
            expiredAt: null,
            isRefreshing: false
        };

        // 从环境变量获取凭证
        this.credentials = {
            clientID: process.env.CLOUD123_CLIENT_ID || '',
            clientSecret: process.env.CLOUD123_CLIENT_SECRET || ''
        };
    }

    /**
     * 获取有效的access_token
     * @returns {Promise<string>} access_token
     */
    async getAccessToken() {
        try {
            // 检查缓存的令牌是否有效
            if (this.isTokenValid()) {
                return this.tokenCache.accessToken;
            }

            // 如果正在刷新令牌，等待完成
            if (this.tokenCache.isRefreshing) {
                return await this.waitForTokenRefresh();
            }

            // 获取新的令牌
            return await this.refreshAccessToken();

        } catch (error) {
            console.error('获取access_token失败:', error);
            throw new Error('无法获取123云盘访问令牌');
        }
    }

    /**
     * 检查令牌是否有效
     * @returns {boolean} 是否有效
     */
    isTokenValid() {
        if (!this.tokenCache.accessToken || !this.tokenCache.expiredAt) {
            return false;
        }

        // 提前5分钟刷新令牌
        const now = new Date();
        const expiredAt = new Date(this.tokenCache.expiredAt);
        const refreshTime = new Date(expiredAt.getTime() - 5 * 60 * 1000);

        return now < refreshTime;
    }

    /**
     * 刷新access_token
     * @returns {Promise<string>} 新的access_token
     */
    async refreshAccessToken() {
        this.tokenCache.isRefreshing = true;

        try {
            const url = `${this.apiConfig.baseUrl}/api/v1/access_token`;

            const requestData = {
                clientID: this.credentials.clientID,
                clientSecret: this.credentials.clientSecret
            };

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Platform': this.apiConfig.platform
                },
                body: JSON.stringify(requestData)
            });

            const result = await response.json();

            if (result.code === 0 && result.data) {
                // 更新缓存
                this.tokenCache.accessToken = result.data.accessToken;
                this.tokenCache.expiredAt = result.data.expiredAt;
                this.tokenCache.isRefreshing = false;

                console.log('123云盘access_token获取成功，过期时间:', result.data.expiredAt);
                return result.data.accessToken;

            } else {
                throw new Error(result.message || '获取access_token失败');
            }

        } catch (error) {
            this.tokenCache.isRefreshing = false;
            console.error('刷新access_token失败:', error);
            throw error;
        }
    }

    /**
     * 等待令牌刷新完成
     * @returns {Promise<string>} access_token
     */
    async waitForTokenRefresh() {
        const maxWaitTime = 10000; // 最大等待10秒
        const checkInterval = 100; // 每100ms检查一次
        let waitTime = 0;

        return new Promise((resolve, reject) => {
            const checkToken = () => {
                if (!this.tokenCache.isRefreshing && this.tokenCache.accessToken) {
                    resolve(this.tokenCache.accessToken);
                } else if (waitTime >= maxWaitTime) {
                    reject(new Error('等待令牌刷新超时'));
                } else {
                    waitTime += checkInterval;
                    setTimeout(checkToken, checkInterval);
                }
            };

            checkToken();
        });
    }

    /**
     * 清除令牌缓存
     */
    clearTokenCache() {
        this.tokenCache = {
            accessToken: null,
            expiredAt: null,
            isRefreshing: false
        };
    }

    /**
     * 验证凭证是否配置
     * @returns {boolean} 是否已配置
     */
    isCredentialsConfigured() {
        return !!(this.credentials.clientID && this.credentials.clientSecret);
    }
}

// 创建全局实例
const cloud123AuthService = new Cloud123AuthService();

// 导出服务
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = cloud123AuthService;
} else {
    // 浏览器环境
    window.Cloud123AuthService = cloud123AuthService;
}
