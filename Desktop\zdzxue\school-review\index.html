<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>校评 - 重庆市梁平区知德中学</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/review.css">
    <!-- 添加字体图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- 添加颜色选择器库 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@simonwep/pickr/dist/themes/classic.min.css">
</head>
<body>
    <!-- 导航栏 -->
    <header>
        <nav class="navbar">
            <div class="logo-container">
                <img src="../images/logo.jpg" alt="知德中学校徽" class="logo">
                <h1 class="school-name">重庆市梁平区知德中学</h1>
            </div>
            <div class="nav-links">
                <a href="../index.html" class="nav-link">首页</a>
                <!-- 管理员按钮，只有管理员才显示 -->
                <a href="../admin/index.html" class="nav-link admin-only" id="adminBtn" style="display: none;">后台管理</a>
                <a href="../school-review/index.html" class="nav-link active">校评</a>
                <a href="../voting/index.html" class="nav-link">投票</a>
            </div>
            <div class="user-actions">
                <div class="user-avatar" id="avatarContainer" style="display: none;">
                    <img src="../images/avatar.png" alt="用户头像" id="userAvatar">
                </div>
                <button class="login-btn" id="loginBtn">登录</button>
            </div>
        </nav>
    </header>

    <!-- 校评页面主体 -->
    <main class="review-main">
        <!-- 页面标题 -->
        <section class="page-header">
            <div class="container">
                <h1 class="page-title">校园评论</h1>
                <p class="page-subtitle">分享你的校园生活，记录美好时光</p>
            </div>
        </section>

        <!-- 搜索和排序区域 -->
        <section class="search-sort-section">
            <div class="container">
                <div class="search-sort-container">
                    <!-- 搜索框 -->
                    <div class="search-box">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" id="searchInput" placeholder="搜索评论标题或内容..." class="search-input">
                        <button class="search-btn" id="searchBtn">搜索</button>
                    </div>

                    <!-- 排序选择 -->
                    <div class="sort-box">
                        <label for="sortSelect" class="sort-label">排序方式：</label>
                        <select id="sortSelect" class="sort-select">
                            <option value="time-desc">时间（从新到旧）</option>
                            <option value="time-asc">时间（从旧到新）</option>
                            <option value="likes-desc">点赞数（从多到少）</option>
                            <option value="likes-asc">点赞数（从少到多）</option>
                        </select>
                    </div>
                </div>
            </div>
        </section>

        <!-- 悬浮添加评论按钮 -->
        <div class="floating-add-btn" id="floatingAddBtn">
            <i class="fas fa-plus"></i>
        </div>

        <!-- 评论列表区域 -->
        <section class="comments-section">
            <div class="container">
                <div class="comments-container" id="commentsContainer">
                    <!-- 评论将通过JavaScript动态加载 -->
                    <div class="loading-indicator" id="loadingIndicator">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>加载中...</span>
                    </div>
                </div>

                <!-- 加载更多按钮 -->
                <div class="load-more-container" id="loadMoreContainer" style="display: none;">
                    <button class="load-more-btn" id="loadMoreBtn">加载更多评论</button>
                </div>
            </div>
        </section>
    </main>

    <!-- 添加评论弹窗 -->
    <div class="modal-overlay" id="commentModalOverlay">
        <div class="comment-modal" id="commentModal">
            <div class="modal-header">
                <h3 class="modal-title">发表评论</h3>
                <button class="modal-close" id="commentModalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="modal-body">
                <form id="commentForm" class="comment-form">
                    <!-- 标题输入 -->
                    <div class="form-group">
                        <label for="commentTitle" class="form-label">标题</label>
                        <input type="text" id="commentTitle" class="form-input" placeholder="请输入评论标题..." maxlength="100" required>
                        <div class="char-count">
                            <span id="titleCharCount">0</span>/100
                        </div>
                    </div>

                    <!-- 内容输入 -->
                    <div class="form-group">
                        <label for="commentContent" class="form-label">内容</label>
                        <textarea id="commentContent" class="form-textarea" placeholder="分享你的校园生活..." maxlength="1000" required></textarea>
                        <div class="char-count">
                            <span id="contentCharCount">0</span>/1000
                        </div>
                    </div>

                    <!-- 图片上传和颜色选择 -->
                    <div class="form-actions">
                        <div class="action-buttons">
                            <!-- 图片上传按钮 -->
                            <button type="button" class="action-btn" id="uploadImageBtn">
                                <i class="fas fa-image"></i>
                                <span>添加图片</span>
                            </button>

                            <!-- 颜色选择按钮 -->
                            <button type="button" class="action-btn" id="colorPickerBtn">
                                <i class="fas fa-palette"></i>
                                <span>选择颜色</span>
                            </button>

                            <!-- 隐藏的文件输入 -->
                            <input type="file" id="imageInput" accept="image/*" multiple style="display: none;">
                        </div>

                        <!-- 颜色选择器容器 -->
                        <div class="color-picker-container" id="colorPickerContainer" style="display: none;">
                            <div class="color-picker-title">
                                <span>选择评论框颜色</span>
                                <button type="button" class="color-picker-close" id="colorPickerClose">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="color-picker" id="colorPicker"></div>
                            <div class="color-preview-section">
                                <div class="color-preview-title">预览效果</div>
                                <div class="color-preview-box" id="colorPreviewBox">
                                    <div class="color-preview-text">这是您的评论框颜色预览</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 已选择的图片预览 -->
                    <div class="image-preview-container" id="imagePreviewContainer" style="display: none;">
                        <h4 class="preview-title">已选择的图片：</h4>
                        <div class="image-preview-list" id="imagePreviewList">
                            <!-- 图片预览将动态添加 -->
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="form-submit">
                        <button type="button" class="cancel-btn" id="cancelCommentBtn">取消</button>
                        <button type="submit" class="submit-btn" id="submitCommentBtn">发表评论</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 回复评论弹窗 -->
    <div class="modal-overlay" id="replyModalOverlay">
        <div class="reply-modal" id="replyModal">
            <div class="modal-header">
                <h3 class="modal-title">回复评论</h3>
                <button class="modal-close" id="replyModalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="modal-body">
                <div class="reply-to-info" id="replyToInfo">
                    <!-- 回复对象信息将动态填充 -->
                </div>

                <form id="replyForm" class="reply-form">
                    <div class="form-group">
                        <label for="replyContent" class="form-label">回复内容</label>
                        <textarea id="replyContent" class="form-textarea" placeholder="写下你的回复..." maxlength="500" required></textarea>
                        <div class="char-count">
                            <span id="replyCharCount">0</span>/500
                        </div>
                    </div>

                    <div class="form-submit">
                        <button type="button" class="cancel-btn" id="cancelReplyBtn">取消</button>
                        <button type="submit" class="submit-btn" id="submitReplyBtn">发表回复</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 登录弹窗（复用主页的登录功能） -->
    <div class="modal-overlay" id="loginModalOverlay">
        <div class="login-modal" id="loginModal">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">用户登录</h3>
                <button class="close-modal" id="closeModal">&times;</button>
            </div>

            <div class="modal-tabs">
                <button class="modal-tab active" id="loginTab">登录</button>
                <button class="modal-tab" id="registerTab">注册</button>
            </div>

            <!-- 登录表单 -->
            <div class="form-tab active" id="loginForm">
                <div class="notification" id="loginNotification"></div>

                <div class="form-group">
                    <label for="loginUsername">用户名</label>
                    <input type="text" class="form-control" id="loginUsername" placeholder="请输入用户名">
                </div>

                <div class="form-group">
                    <label for="loginPassword">密码</label>
                    <input type="password" class="form-control" id="loginPassword" placeholder="请输入密码">
                </div>

                <div class="form-footer">
                    <div class="remember-me">
                        <input type="checkbox" id="rememberMe">
                        <label for="rememberMe">记住我</label>
                    </div>
                    <a href="#" class="forgot-password">忘记密码?</a>
                </div>

                <button class="submit-btn" id="loginButton">登录</button>

                <div class="register-prompt">
                    还没有账号? <a href="#" class="register-link" id="switchToRegister">立即注册</a>
                </div>
            </div>

            <!-- 注册表单 -->
            <div class="form-tab" id="registerForm">
                <div class="notification" id="registerNotification"></div>

                <div class="form-group">
                    <label for="registerUsername">用户名</label>
                    <input type="text" class="form-control" id="registerUsername" placeholder="请设置用户名">
                </div>

                <div class="form-group">
                    <label for="registerPassword">密码</label>
                    <input type="password" class="form-control" id="registerPassword" placeholder="请设置密码">
                </div>

                <div class="form-group">
                    <label for="confirmPassword">确认密码</label>
                    <input type="password" class="form-control" id="confirmPassword" placeholder="请再次输入密码">
                </div>

                <button class="submit-btn" id="registerButton">注册</button>

                <div class="register-prompt">
                    已有账号? <a href="#" class="register-link" id="switchToLogin">立即登录</a>
                </div>
            </div>
        </div>
    </div>

    <!-- 个人信息弹窗 -->
    <div class="modal-overlay" id="profileModalOverlay">
        <div class="profile-modal" id="profileModal">
            <div class="modal-header">
                <h3 class="modal-title">个人信息</h3>
                <button class="close-modal" id="closeProfileModal">&times;</button>
            </div>

            <div class="profile-content">
                <div class="notification" id="profileNotification"></div>

                <div class="avatar-section">
                    <div class="current-avatar">
                        <img src="../images/avatar.png" alt="当前头像" id="currentAvatar">
                    </div>
                    <div class="avatar-upload">
                        <label for="avatarUpload" class="avatar-upload-label">更换头像</label>
                        <input type="file" id="avatarUpload" accept="image/*" style="display: none;">
                    </div>
                </div>

                <div class="form-group">
                    <label for="profileUsername">用户名</label>
                    <input type="text" class="form-control" id="profileUsername" placeholder="请输入新用户名">
                </div>

                <div class="user-id-section">
                    <p>用户ID: <span id="userUniqueId">UID_XXXXX</span></p>
                    <p class="id-note">（此ID为您的唯一身份标识，无法修改）</p>
                </div>

                <div class="profile-buttons">
                    <button class="submit-btn" id="saveProfileButton">保存修改</button>
                    <button class="logout-btn" id="logoutButton">退出登录</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script>
        // 123云盘API配置 - 图床目录ID（需要在环境变量中配置）
        // access_token将通过API自动获取
        window.CLOUD123_PARENT_FILE_ID = ''; // 需要在Vercel环境变量中配置CLOUD123_PARENT_FILE_ID
    </script>
    <script src="https://cdn.jsdelivr.net/npm/@simonwep/pickr/dist/pickr.min.js"></script>
    <script src="../js/main.js"></script>
    <script src="js/image-upload.js"></script>
    <script src="js/comment-system.js"></script>
    <script src="js/review.js"></script>
</body>
</html>
