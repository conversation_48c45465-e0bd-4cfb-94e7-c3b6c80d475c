import requests
import json

def check_token_expiry(client_id, client_secret):
    # API端点URL
    url = 'https://open-api.123pan.com/api/v1/access_token'
    
    # 请求头
    headers = {
        'Platform': 'open_platform',
        'Content-Type': 'application/json'
    }
    
    # 请求体数据
    data = {
        'clientID': client_id,
        'clientSecret': client_secret
    }
    
    try:
        # 发送POST请求
        response = requests.post(url, headers=headers, data=json.dumps(data))
        response.raise_for_status()  # 检查请求是否成功
        
        # 解析JSON响应
        result = response.json()
        
        # 提取并返回过期时间
        if 'data' in result and 'expiredAt' in result['data']:
            return result['data']['expiredAt']
        else:
            return '无法获取过期时间，响应数据格式不正确'
            
    except requests.exceptions.RequestException as e:
        return f'请求失败: {str(e)}'

# 使用示例
if __name__ == '__main__':
    # 替换为您的实际clientID和clientSecret
    expiry_time = check_token_expiry('64ebac30fa2b4f1ebb07210eb2c3eb40', '95a1700d1ac543b68eeccd4785a5d51b')
    print(f'Token过期时间: {expiry_time}')