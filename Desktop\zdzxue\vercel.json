{"version": 2, "name": "zhide-school-website", "public": true, "github": {"silent": true}, "buildCommand": "", "outputDirectory": ".", "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/css/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Security-Policy", "value": "default-src 'self'"}, {"key": "Content-Type", "value": "text/css; charset=utf-8"}, {"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/js/(.*)", "headers": [{"key": "Content-Type", "value": "application/javascript; charset=utf-8"}, {"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/images/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)\\.html", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}, {"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "no-store"}, {"key": "Content-Type", "value": "application/json"}]}], "rewrites": [{"source": "/", "destination": "/index.html"}, {"source": "/school-review", "destination": "/school-review/index.html"}, {"source": "/school-review/(.*)", "destination": "/school-review/$1"}, {"source": "/admin", "destination": "/admin/index.html"}, {"source": "/admin/(.*)", "destination": "/admin/$1"}, {"source": "/voting", "destination": "/voting/index.html"}, {"source": "/voting/(.*)", "destination": "/voting/$1"}, {"source": "/api/(.*)", "destination": "/api/$1"}], "redirects": [{"source": "/home", "destination": "/", "permanent": true}, {"source": "/review", "destination": "/school-review", "permanent": true}], "trailingSlash": false, "cleanUrls": true}