/**
 * Vercel API端点：获取123云盘access_token
 * 路径: /api/cloud123-token
 */

export default async function handler(req, res) {
    // 只允许POST请求
    if (req.method !== 'POST') {
        return res.status(405).json({ 
            success: false, 
            error: 'Method not allowed' 
        });
    }

    try {
        // 从环境变量获取凭证
        const clientID = process.env.CLOUD123_CLIENT_ID;
        const clientSecret = process.env.CLOUD123_CLIENT_SECRET;

        if (!clientID || !clientSecret) {
            return res.status(500).json({
                success: false,
                error: '123云盘凭证未配置'
            });
        }

        // 调用123云盘API获取access_token
        const response = await fetch('https://open-api.123pan.com/api/v1/access_token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Platform': 'open_platform'
            },
            body: JSON.stringify({
                clientID: clientID,
                clientSecret: clientSecret
            })
        });

        const result = await response.json();

        if (result.code === 0 && result.data) {
            // 返回成功结果
            return res.status(200).json({
                success: true,
                data: {
                    accessToken: result.data.accessToken,
                    expiredAt: result.data.expiredAt
                }
            });
        } else {
            // 返回API错误
            return res.status(400).json({
                success: false,
                error: result.message || '获取access_token失败',
                code: result.code
            });
        }

    } catch (error) {
        console.error('获取123云盘access_token失败:', error);
        return res.status(500).json({
            success: false,
            error: '服务器内部错误'
        });
    }
}
